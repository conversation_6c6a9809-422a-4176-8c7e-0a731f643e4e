"""
TBTrade Web可视化界面 - 主应用入口
使用Streamlit构建的量化交易系统Web界面
"""

import streamlit as st
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径，以便导入现有TBTrade模块
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 页面配置
st.set_page_config(
    page_title="TBTrade 量化交易系统",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded",
    menu_items={
        'Get Help': 'https://github.com/your-repo/tbtrade',
        'Report a bug': 'https://github.com/your-repo/tbtrade/issues',
        'About': "TBTrade - 专业级量化交易系统"
    }
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-container {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .status-good {
        color: #28a745;
        font-weight: bold;
    }
    .status-warning {
        color: #ffc107;
        font-weight: bold;
    }
    .status-error {
        color: #dc3545;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """主应用函数"""
    
    # 主标题
    st.markdown('<h1 class="main-header">🚀 TBTrade - 动态仓位管理交易系统</h1>', 
                unsafe_allow_html=True)
    
    # 系统状态概览
    st.subheader("📊 系统概览")
    
    # 创建指标列
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="💰 总资产",
            value="¥10,000",
            delta="2.3%",
            help="当前账户总资产价值"
        )
    
    with col2:
        st.metric(
            label="📈 今日收益",
            value="¥230",
            delta="2.3%",
            help="今日收益金额和收益率"
        )
    
    with col3:
        st.metric(
            label="📦 持仓数量",
            value="3",
            delta="1",
            help="当前持有的币种数量"
        )
    
    with col4:
        st.metric(
            label="🎯 胜率",
            value="68.5%",
            delta="1.2%",
            help="历史交易胜率"
        )
    
    # 分隔线
    st.divider()
    
    # 快速操作区域
    st.subheader("⚡ 快速操作")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🔄 刷新数据", type="primary", use_container_width=True):
            st.success("数据刷新完成！")
            st.rerun()
    
    with col2:
        if st.button("📊 快速回测", use_container_width=True):
            st.info("跳转到回测分析页面...")
            st.switch_page("pages/01_📊_回测分析.py")
    
    with col3:
        if st.button("📈 实时监控", use_container_width=True):
            st.info("跳转到系统监控页面...")
            st.switch_page("pages/03_📈_系统监控.py")
    
    # 系统状态
    st.subheader("🔧 系统状态")
    
    status_col1, status_col2 = st.columns(2)
    
    with status_col1:
        st.markdown("**数据连接状态**")
        st.markdown('<span class="status-good">🟢 正常</span>', unsafe_allow_html=True)
        st.markdown("**策略引擎状态**")
        st.markdown('<span class="status-good">🟢 运行中</span>', unsafe_allow_html=True)
    
    with status_col2:
        st.markdown("**市场数据更新**")
        st.markdown(f'<span class="status-good">🟢 {datetime.now().strftime("%H:%M:%S")}</span>', 
                   unsafe_allow_html=True)
        st.markdown("**风险控制系统**")
        st.markdown('<span class="status-good">🟢 正常</span>', unsafe_allow_html=True)
    
    # 最近活动
    st.subheader("📝 最近活动")
    
    # 模拟最近活动数据
    recent_activities = [
        {"time": "10:30:25", "type": "交易", "content": "BTCUSDT 买入信号触发", "status": "success"},
        {"time": "10:25:12", "content": "数据更新完成", "type": "系统", "status": "info"},
        {"time": "10:20:08", "type": "回测", "content": "EMA策略回测完成", "status": "success"},
        {"time": "10:15:33", "type": "监控", "content": "系统健康检查通过", "status": "info"},
    ]
    
    for activity in recent_activities:
        with st.container():
            col1, col2, col3 = st.columns([2, 2, 6])
            with col1:
                st.text(activity["time"])
            with col2:
                if activity["status"] == "success":
                    st.success(activity["type"])
                else:
                    st.info(activity["type"])
            with col3:
                st.text(activity["content"])
    
    # 侧边栏信息
    with st.sidebar:
        st.header("🔧 系统信息")
        st.info(f"**当前时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        st.info("**版本**: v1.0.0")
        st.info("**运行时间**: 2小时15分钟")
        
        st.header("📚 快速导航")
        st.page_link("pages/01_📊_回测分析.py", label="📊 回测分析", icon="📊")
        st.page_link("pages/02_💹_实时交易.py", label="💹 实时交易", icon="💹")
        st.page_link("pages/03_📈_系统监控.py", label="📈 系统监控", icon="📈")
        st.page_link("pages/04_💾_数据管理.py", label="💾 数据管理", icon="💾")
        
        st.header("⚙️ 系统设置")
        auto_refresh = st.checkbox("自动刷新", value=True)
        refresh_interval = st.slider("刷新间隔(秒)", 1, 60, 5)
        
        if auto_refresh:
            st.info(f"页面将每{refresh_interval}秒自动刷新")

if __name__ == "__main__":
    main()
