"""
TBTrade系统集成接口
提供Web界面与现有TBTrade系统的集成功能
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import sqlite3
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 导入现有TBTrade模块
try:
    from src.strategies.backtest_engine import BacktestEngine
    from src.strategies.ema_dynamic_strategy import EMADynamicStrategy
    from src.data_layer.historical_data_fetcher import scan_local_databases
    from run_dynamic_backtest import DynamicBacktestRunner
    from backtest_visualization import BacktestVisualizer
except ImportError as e:
    print(f"警告: 无法导入TBTrade模块: {e}")
    # 在开发阶段可以继续运行，但功能会受限
    BacktestEngine = None
    EMADynamicStrategy = None

class TBTradeIntegration:
    """TBTrade系统集成类"""
    
    def __init__(self):
        """初始化集成接口"""
        self.project_root = project_root
        self.data_dir = project_root / "data"
        self.config_dir = project_root / "config"
        self.logs_dir = project_root / "logs"
        self.backtest_results_dir = project_root / "backtest_results"
        
        # 确保目录存在
        self._ensure_directories()
        
        # 初始化组件
        self.backtest_engine = None
        self.dynamic_runner = None
        self.visualizer = None
        
    def _ensure_directories(self):
        """确保必要的目录存在"""
        for directory in [self.data_dir, self.logs_dir, self.backtest_results_dir]:
            directory.mkdir(exist_ok=True)
    
    def get_available_symbols(self) -> List[str]:
        """获取可用的交易币种列表"""
        try:
            databases = scan_local_databases(str(self.data_dir))
            symbols = []
            
            for db_info in databases:
                if 'symbol' in db_info:
                    symbols.append(db_info['symbol'])
            
            return sorted(list(set(symbols)))
        except Exception as e:
            print(f"获取币种列表失败: {e}")
            # 返回默认币种列表
            return ["BTCUSDT", "ETHUSDT", "ADAUSDT", "BNBUSDT", "SOLUSDT"]
    
    def get_data_info(self, symbol: str = None) -> Dict[str, Any]:
        """获取数据信息"""
        try:
            databases = scan_local_databases(str(self.data_dir))
            
            if symbol:
                # 获取特定币种的数据信息
                for db_info in databases:
                    if db_info.get('symbol') == symbol:
                        return {
                            'symbol': symbol,
                            'database': str(db_info['db_file']),
                            'table': db_info['table_name'],
                            'data_count': db_info.get('data_count', 0),
                            'date_range': {
                                'start': db_info.get('start_date'),
                                'end': db_info.get('end_date')
                            }
                        }
                return {'error': f'未找到币种 {symbol} 的数据'}
            else:
                # 获取所有数据概览
                summary = {
                    'total_symbols': len(databases),
                    'databases': [],
                    'total_records': 0
                }
                
                for db_info in databases:
                    summary['databases'].append({
                        'symbol': db_info.get('symbol'),
                        'records': db_info.get('data_count', 0),
                        'date_range': {
                            'start': db_info.get('start_date'),
                            'end': db_info.get('end_date')
                        }
                    })
                    summary['total_records'] += db_info.get('data_count', 0)
                
                return summary
        except Exception as e:
            return {'error': f'获取数据信息失败: {e}'}
    
    def load_symbol_data(self, symbol: str, start_date: str = None, 
                        end_date: str = None) -> pd.DataFrame:
        """加载币种数据"""
        try:
            databases = scan_local_databases(str(self.data_dir))
            
            # 找到对应的数据库信息
            db_info = None
            for db in databases:
                if db.get('symbol') == symbol:
                    db_info = db
                    break
            
            if not db_info:
                raise ValueError(f"未找到币种 {symbol} 的数据")
            
            # 连接数据库
            conn = sqlite3.connect(str(db_info['db_file']))
            
            # 构建查询
            query = f"""
            SELECT datetime_str as datetime, open_price as open, high_price as high,
                   low_price as low, close_price as close, volume
            FROM {db_info['table_name']}
            WHERE symbol = ?
            """
            params = [symbol]
            
            if start_date:
                query += " AND datetime_str >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND datetime_str <= ?"
                params.append(end_date)
            
            query += " ORDER BY datetime_str"
            
            # 执行查询
            df = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            if df.empty:
                raise ValueError(f"未找到符合条件的数据")
            
            # 数据预处理
            df['datetime'] = pd.to_datetime(df['datetime'])
            df.set_index('datetime', inplace=True)
            
            # 确保数据类型正确
            for col in ['open', 'high', 'low', 'close', 'volume']:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            return df
            
        except Exception as e:
            raise Exception(f"加载数据失败: {e}")
    
    def run_backtest(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """运行回测"""
        try:
            if not EMADynamicStrategy:
                raise Exception("TBTrade模块未正确加载")
            
            # 解析配置
            symbols = config.get('symbols', ['BTCUSDT'])
            initial_capital = config.get('initial_capital', 10000)
            start_date = config.get('start_date')
            end_date = config.get('end_date')
            strategy_params = config.get('strategy_params', {})
            
            # 创建动态回测运行器
            runner = DynamicBacktestRunner(initial_capital=initial_capital)
            
            # 创建策略实例
            strategy = EMADynamicStrategy(
                initial_capital=initial_capital,
                enable_partial_profit=config.get('enable_partial_profit', True),
                verbose_level=config.get('verbose_level', 1)
            )
            
            # 更新策略参数
            if strategy_params:
                strategy.update_params(strategy_params)
            
            # 运行回测
            results = runner.run_backtest(
                symbols=symbols,
                start_date=start_date,
                end_date=end_date,
                strategy=strategy
            )
            
            return {
                'success': True,
                'results': results,
                'config': config,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'config': config,
                'timestamp': datetime.now().isoformat()
            }
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            status = {
                'timestamp': datetime.now().isoformat(),
                'database_status': 'unknown',
                'data_status': 'unknown',
                'strategy_status': 'unknown',
                'system_info': {}
            }
            
            # 检查数据库状态
            try:
                databases = scan_local_databases(str(self.data_dir))
                status['database_status'] = 'connected'
                status['system_info']['total_databases'] = len(databases)
            except:
                status['database_status'] = 'error'
            
            # 检查数据状态
            try:
                symbols = self.get_available_symbols()
                status['data_status'] = 'available'
                status['system_info']['available_symbols'] = len(symbols)
            except:
                status['data_status'] = 'error'
            
            # 检查策略状态
            try:
                if EMADynamicStrategy:
                    status['strategy_status'] = 'ready'
                else:
                    status['strategy_status'] = 'error'
            except:
                status['strategy_status'] = 'error'
            
            # 系统信息
            status['system_info'].update({
                'project_root': str(self.project_root),
                'data_directory': str(self.data_dir),
                'logs_directory': str(self.logs_dir),
                'python_version': sys.version,
                'platform': sys.platform
            })
            
            return status
            
        except Exception as e:
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'database_status': 'error',
                'data_status': 'error',
                'strategy_status': 'error'
            }
    
    def get_recent_logs(self, log_type: str = 'trading', lines: int = 100) -> List[str]:
        """获取最近的日志"""
        try:
            log_file = self.logs_dir / f"{log_type}.log"
            
            if not log_file.exists():
                return []
            
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                return all_lines[-lines:] if len(all_lines) > lines else all_lines
                
        except Exception as e:
            return [f"读取日志失败: {e}"]
    
    def update_data(self, symbols: List[str] = None) -> Dict[str, Any]:
        """更新数据"""
        try:
            # 这里应该调用数据更新功能
            # 由于现有系统的数据更新逻辑比较复杂，这里先返回模拟结果
            
            if not symbols:
                symbols = self.get_available_symbols()[:5]  # 默认更新前5个币种
            
            result = {
                'success': True,
                'updated_symbols': symbols,
                'timestamp': datetime.now().isoformat(),
                'message': f'已触发 {len(symbols)} 个币种的数据更新'
            }
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

# 创建全局集成实例
tbtrade_integration = TBTradeIntegration()
