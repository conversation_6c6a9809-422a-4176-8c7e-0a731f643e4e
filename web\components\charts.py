"""
图表组件模块
提供各种金融图表的Streamlit组件
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

def create_equity_curve_chart(equity_data: pd.DataFrame, title: str = "资产曲线") -> go.Figure:
    """
    创建资产曲线图表
    
    Args:
        equity_data: 包含时间和资产价值的DataFrame
        title: 图表标题
    
    Returns:
        plotly图表对象
    """
    fig = go.Figure()
    
    # 添加资产曲线
    fig.add_trace(go.Scatter(
        x=equity_data.index,
        y=equity_data['equity'],
        mode='lines',
        name='资产价值',
        line=dict(color='#1f77b4', width=2),
        hovertemplate='<b>时间</b>: %{x}<br><b>资产</b>: ¥%{y:,.2f}<extra></extra>'
    ))
    
    # 添加基准线
    if 'benchmark' in equity_data.columns:
        fig.add_trace(go.Scatter(
            x=equity_data.index,
            y=equity_data['benchmark'],
            mode='lines',
            name='基准',
            line=dict(color='#ff7f0e', width=1, dash='dash'),
            hovertemplate='<b>时间</b>: %{x}<br><b>基准</b>: ¥%{y:,.2f}<extra></extra>'
        ))
    
    # 更新布局
    fig.update_layout(
        title=title,
        xaxis_title="时间",
        yaxis_title="资产价值 (¥)",
        hovermode='x unified',
        showlegend=True,
        height=500,
        margin=dict(l=50, r=50, t=50, b=50)
    )
    
    return fig

def create_candlestick_chart(ohlc_data: pd.DataFrame, 
                           indicators: Optional[Dict] = None,
                           title: str = "K线图") -> go.Figure:
    """
    创建K线图表
    
    Args:
        ohlc_data: OHLC数据
        indicators: 技术指标数据
        title: 图表标题
    
    Returns:
        plotly图表对象
    """
    # 创建子图
    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.1,
        subplot_titles=('价格', '成交量'),
        row_width=[0.7, 0.3]
    )
    
    # 添加K线图
    fig.add_trace(go.Candlestick(
        x=ohlc_data.index,
        open=ohlc_data['open'],
        high=ohlc_data['high'],
        low=ohlc_data['low'],
        close=ohlc_data['close'],
        name="K线",
        increasing_line_color='#26a69a',
        decreasing_line_color='#ef5350'
    ), row=1, col=1)
    
    # 添加技术指标
    if indicators:
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']
        for i, (name, data) in enumerate(indicators.items()):
            fig.add_trace(go.Scatter(
                x=ohlc_data.index,
                y=data,
                mode='lines',
                name=name,
                line=dict(color=colors[i % len(colors)], width=1)
            ), row=1, col=1)
    
    # 添加成交量
    fig.add_trace(go.Bar(
        x=ohlc_data.index,
        y=ohlc_data['volume'],
        name="成交量",
        marker_color='rgba(158,202,225,0.6)'
    ), row=2, col=1)
    
    # 更新布局
    fig.update_layout(
        title=title,
        xaxis_rangeslider_visible=False,
        height=600,
        showlegend=True,
        margin=dict(l=50, r=50, t=50, b=50)
    )
    
    return fig

def create_returns_distribution_chart(returns: pd.Series, title: str = "收益率分布") -> go.Figure:
    """
    创建收益率分布图表
    
    Args:
        returns: 收益率序列
        title: 图表标题
    
    Returns:
        plotly图表对象
    """
    fig = go.Figure()
    
    # 添加直方图
    fig.add_trace(go.Histogram(
        x=returns,
        nbinsx=50,
        name="收益率分布",
        marker_color='rgba(31, 119, 180, 0.7)',
        hovertemplate='<b>收益率区间</b>: %{x}<br><b>频次</b>: %{y}<extra></extra>'
    ))
    
    # 添加正态分布拟合线
    x_range = np.linspace(returns.min(), returns.max(), 100)
    normal_dist = np.exp(-0.5 * ((x_range - returns.mean()) / returns.std()) ** 2)
    normal_dist = normal_dist / normal_dist.max() * returns.count() / 10
    
    fig.add_trace(go.Scatter(
        x=x_range,
        y=normal_dist,
        mode='lines',
        name='正态分布拟合',
        line=dict(color='red', width=2, dash='dash')
    ))
    
    # 更新布局
    fig.update_layout(
        title=title,
        xaxis_title="收益率",
        yaxis_title="频次",
        showlegend=True,
        height=400,
        margin=dict(l=50, r=50, t=50, b=50)
    )
    
    return fig

def create_drawdown_chart(drawdown_data: pd.Series, title: str = "最大回撤") -> go.Figure:
    """
    创建回撤图表
    
    Args:
        drawdown_data: 回撤数据
        title: 图表标题
    
    Returns:
        plotly图表对象
    """
    fig = go.Figure()
    
    # 添加回撤面积图
    fig.add_trace(go.Scatter(
        x=drawdown_data.index,
        y=drawdown_data,
        fill='tonexty',
        mode='lines',
        name='回撤',
        line=dict(color='red', width=1),
        fillcolor='rgba(255, 0, 0, 0.3)',
        hovertemplate='<b>时间</b>: %{x}<br><b>回撤</b>: %{y:.2%}<extra></extra>'
    ))
    
    # 添加零线
    fig.add_hline(y=0, line_dash="dash", line_color="black", opacity=0.5)
    
    # 更新布局
    fig.update_layout(
        title=title,
        xaxis_title="时间",
        yaxis_title="回撤比例",
        yaxis_tickformat='.1%',
        showlegend=False,
        height=300,
        margin=dict(l=50, r=50, t=50, b=50)
    )
    
    return fig

def create_performance_metrics_chart(metrics: Dict, title: str = "绩效指标") -> go.Figure:
    """
    创建绩效指标雷达图
    
    Args:
        metrics: 绩效指标字典
        title: 图表标题
    
    Returns:
        plotly图表对象
    """
    # 标准化指标值到0-1范围
    normalized_metrics = {}
    for key, value in metrics.items():
        if key == "胜率":
            normalized_metrics[key] = value
        elif key == "夏普比率":
            normalized_metrics[key] = min(max(value / 3, 0), 1)  # 假设3为优秀值
        elif key == "最大回撤":
            normalized_metrics[key] = max(1 + value, 0)  # 回撤为负值，转换为正值
        else:
            normalized_metrics[key] = min(max(value, 0), 1)
    
    categories = list(normalized_metrics.keys())
    values = list(normalized_metrics.values())
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatterpolar(
        r=values,
        theta=categories,
        fill='toself',
        name='当前策略',
        line_color='#1f77b4',
        fillcolor='rgba(31, 119, 180, 0.3)'
    ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 1]
            )),
        title=title,
        showlegend=True,
        height=400,
        margin=dict(l=50, r=50, t=50, b=50)
    )
    
    return fig
