"""
回测分析页面
提供交互式回测配置和结果可视化功能
"""

import streamlit as st
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import plotly.graph_objects as go

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入组件和配置
from components.charts import create_equity_curve_chart, create_returns_distribution_chart
from components.tables import display_trades_table, display_performance_summary
from config import BACKTEST_CONFIG, CHART_CONFIG
from utils.tbtrade_integration import tbtrade_integration
from utils.data_loader import get_available_symbols, load_market_data

# 页面配置
st.set_page_config(
    page_title="回测分析 - TBTrade",
    page_icon="📊",
    layout="wide"
)

def main():
    """主函数"""
    
    # 页面标题
    st.title("📊 回测分析")
    st.markdown("---")
    
    # 侧边栏配置
    with st.sidebar:
        st.header("🔧 回测配置")
        
        # 基础参数
        st.subheader("基础参数")
        initial_capital = st.number_input(
            "初始资金 (¥)",
            min_value=1000,
            max_value=1000000,
            value=BACKTEST_CONFIG["default_capital"],
            step=1000,
            help="回测的初始资金金额"
        )
        
        # 币种选择
        st.subheader("币种选择")
        try:
            available_symbols = get_available_symbols()
        except:
            available_symbols = BACKTEST_CONFIG["default_symbols"] + [
                "BNBUSDT", "SOLUSDT", "XRPUSDT", "DOGEUSDT", "AVAXUSDT",
                "DOTUSDT", "MATICUSDT", "LTCUSDT", "LINKUSDT", "UNIUSDT"
            ]

        selected_symbols = st.multiselect(
            "选择交易币种",
            options=available_symbols,
            default=available_symbols[:3] if len(available_symbols) >= 3 else available_symbols,
            help="选择要进行回测的币种"
        )
        
        # 时间范围
        st.subheader("时间范围")
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=365)
        
        date_range = st.date_input(
            "选择回测时间范围",
            value=(start_date, end_date),
            min_value=datetime(2022, 1, 1).date(),
            max_value=end_date,
            help="选择回测的开始和结束日期"
        )
        
        # 策略参数
        st.subheader("策略参数")
        ema_short = st.slider("EMA短期", 5, 50, 21, help="短期EMA周期")
        ema_medium = st.slider("EMA中期", 20, 100, 55, help="中期EMA周期")
        ema_long = st.slider("EMA长期", 100, 300, 200, help="长期EMA周期")
        
        # 风险管理
        st.subheader("风险管理")
        stop_loss_pct = st.slider("止损比例", 0.05, 0.30, 0.15, 0.01, format="%.2f", help="止损百分比")
        take_profit_pct = st.slider("止盈比例", 0.10, 1.00, 0.50, 0.05, format="%.2f", help="分批止盈百分比")
        
        # 高级选项
        with st.expander("🔧 高级选项"):
            enable_partial_profit = st.checkbox("启用分批止盈", value=True)
            signal_cooldown_days = st.slider("信号冷却期(天)", 1, 60, 30)
            max_positions = st.slider("最大持仓数", 1, 10, 5)
        
        # 回测按钮
        st.markdown("---")
        run_backtest = st.button("🚀 开始回测", type="primary", use_container_width=True)
    
    # 主内容区域
    if run_backtest:
        if not selected_symbols:
            st.error("请至少选择一个交易币种")
            return
        
        if len(date_range) != 2:
            st.error("请选择完整的时间范围")
            return
        
        # 显示回测进度
        progress_bar = st.progress(0)
        status_text = st.empty()

        # 运行真实回测
        with st.spinner("正在运行回测..."):
            try:
                # 构建回测配置
                backtest_config = {
                    'symbols': selected_symbols,
                    'initial_capital': initial_capital,
                    'start_date': date_range[0].strftime('%Y-%m-%d'),
                    'end_date': date_range[1].strftime('%Y-%m-%d'),
                    'strategy_params': {
                        'ema_short': ema_short,
                        'ema_medium': ema_medium,
                        'ema_long': ema_long,
                        'stop_loss_pct': stop_loss_pct,
                        'take_profit_pct': take_profit_pct,
                        'signal_cooldown_days': signal_cooldown_days
                    },
                    'enable_partial_profit': enable_partial_profit,
                    'verbose_level': 1
                }

                # 更新进度
                progress_bar.progress(20)
                status_text.text("正在加载历史数据...")

                # 调用TBTrade集成接口
                results = tbtrade_integration.run_backtest(backtest_config)

                progress_bar.progress(100)
                status_text.text("回测完成！")

            except Exception as e:
                st.error(f"回测执行失败: {e}")
                # 如果真实回测失败，使用模拟数据
                results = {
                    'success': False,
                    'error': str(e),
                    'use_mock': True
                }
        
        # 清除进度显示
        progress_bar.empty()
        status_text.empty()

        # 显示回测结果
        if results.get('success', False):
            st.success("✅ 回测完成！")
            # 显示真实回测结果
            display_backtest_results(results['results'])
        else:
            if results.get('use_mock', False):
                st.warning("⚠️ 使用模拟数据进行回测演示")
                # 生成模拟数据用于演示
                mock_results = generate_mock_backtest_results(
                    selected_symbols, date_range, initial_capital
                )
                display_backtest_results(mock_results)
            else:
                st.error(f"❌ 回测失败: {results.get('error', '未知错误')}")
    
    else:
        # 显示默认内容
        st.info("👈 请在左侧配置回测参数，然后点击"开始回测"按钮")
        
        # 显示历史回测记录
        st.subheader("📚 历史回测记录")
        
        # 模拟历史记录
        history_data = [
            {
                "时间": "2024-01-15 10:30",
                "币种": "BTCUSDT, ETHUSDT",
                "初始资金": "¥10,000",
                "最终资金": "¥11,250",
                "收益率": "12.5%",
                "状态": "已完成"
            },
            {
                "时间": "2024-01-10 14:20",
                "币种": "ADAUSDT, DOTUSDT",
                "初始资金": "¥20,000",
                "最终资金": "¥18,900",
                "收益率": "-5.5%",
                "状态": "已完成"
            }
        ]
        
        history_df = pd.DataFrame(history_data)
        st.dataframe(history_df, use_container_width=True, hide_index=True)

def generate_mock_backtest_results(symbols, date_range, initial_capital):
    """生成模拟回测结果"""
    start_date, end_date = date_range
    days = (end_date - start_date).days
    
    # 生成模拟资产曲线
    dates = pd.date_range(start=start_date, end=end_date, freq='D')
    returns = np.random.normal(0.001, 0.02, len(dates))  # 模拟日收益率
    equity_values = [initial_capital]
    
    for ret in returns[1:]:
        equity_values.append(equity_values[-1] * (1 + ret))
    
    equity_df = pd.DataFrame({
        'equity': equity_values,
        'benchmark': [initial_capital * (1 + 0.0005) ** i for i in range(len(dates))]
    }, index=dates)
    
    # 生成模拟交易记录
    num_trades = np.random.randint(5, 20)
    trades_data = []
    
    for i in range(num_trades):
        symbol = np.random.choice(symbols)
        entry_date = start_date + timedelta(days=np.random.randint(0, days-10))
        exit_date = entry_date + timedelta(days=np.random.randint(1, 10))
        
        entry_price = np.random.uniform(20000, 50000)
        exit_price = entry_price * (1 + np.random.normal(0, 0.1))
        quantity = 1000 / entry_price
        pnl = (exit_price - entry_price) * quantity
        
        trades_data.append({
            'symbol': symbol,
            'entry_date': entry_date,
            'exit_date': exit_date,
            'entry_price': entry_price,
            'exit_price': exit_price,
            'quantity': quantity,
            'pnl': pnl,
            'return_pct': (exit_price - entry_price) / entry_price
        })
    
    trades_df = pd.DataFrame(trades_data)
    
    # 生成绩效指标
    total_return = (equity_values[-1] - initial_capital) / initial_capital
    winning_trades = len(trades_df[trades_df['pnl'] > 0])
    win_rate = winning_trades / len(trades_df) if len(trades_df) > 0 else 0
    
    performance_metrics = {
        "总收益率": total_return,
        "年化收益率": total_return * (365 / days),
        "胜率": win_rate,
        "总交易次数": len(trades_df),
        "盈利交易": winning_trades,
        "亏损交易": len(trades_df) - winning_trades,
        "最大回撤": -0.15,  # 模拟值
        "夏普比率": 1.2,    # 模拟值
        "总盈亏": equity_values[-1] - initial_capital,
        "最大单笔盈利": trades_df['pnl'].max() if not trades_df.empty else 0,
        "最大单笔亏损": trades_df['pnl'].min() if not trades_df.empty else 0
    }
    
    return {
        'equity_curve': equity_df,
        'trades': trades_df,
        'performance': performance_metrics,
        'symbols': symbols,
        'date_range': date_range,
        'initial_capital': initial_capital
    }

def display_backtest_results(results):
    """显示回测结果"""
    
    # 关键指标概览
    st.subheader("📈 关键指标")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_return = results['performance']['总收益率']
        st.metric(
            "总收益率",
            f"{total_return:.2%}",
            delta=f"{total_return:.2%}",
            help="整个回测期间的总收益率"
        )
    
    with col2:
        win_rate = results['performance']['胜率']
        st.metric(
            "胜率",
            f"{win_rate:.1%}",
            help="盈利交易占总交易的比例"
        )
    
    with col3:
        total_trades = results['performance']['总交易次数']
        st.metric(
            "总交易次数",
            total_trades,
            help="回测期间的总交易次数"
        )
    
    with col4:
        sharpe_ratio = results['performance']['夏普比率']
        st.metric(
            "夏普比率",
            f"{sharpe_ratio:.2f}",
            help="风险调整后的收益指标"
        )
    
    # 资产曲线图
    st.subheader("💰 资产曲线")
    equity_chart = create_equity_curve_chart(results['equity_curve'])
    st.plotly_chart(equity_chart, use_container_width=True)
    
    # 收益率分布
    if not results['trades'].empty:
        st.subheader("📊 收益率分布")
        returns_chart = create_returns_distribution_chart(results['trades']['return_pct'])
        st.plotly_chart(returns_chart, use_container_width=True)
    
    # 交易记录表格
    display_trades_table(results['trades'])
    
    # 详细绩效指标
    display_performance_summary(results['performance'])

if __name__ == "__main__":
    main()
