# TBTrade Web可视化界面

## 项目结构

```
web/
├── backend/                 # Flask后端应用
│   ├── app.py              # 主应用入口
│   ├── config.py           # 配置管理
│   ├── requirements.txt    # Python依赖
│   ├── api/                # API路由模块
│   │   ├── __init__.py
│   │   ├── backtest.py     # 回测相关API
│   │   ├── trading.py      # 交易相关API
│   │   ├── monitoring.py   # 监控相关API
│   │   └── data.py         # 数据管理API
│   ├── services/           # 业务逻辑服务
│   │   ├── __init__.py
│   │   ├── backtest_service.py
│   │   ├── trading_service.py
│   │   └── data_service.py
│   ├── websocket/          # WebSocket处理
│   │   ├── __init__.py
│   │   └── handlers.py
│   └── utils/              # 工具函数
│       ├── __init__.py
│       └── helpers.py
├── frontend/               # Vue.js前端应用
│   ├── package.json        # Node.js依赖
│   ├── vite.config.js      # Vite配置
│   ├── index.html          # 入口HTML
│   ├── src/                # 源代码
│   │   ├── main.js         # 应用入口
│   │   ├── App.vue         # 根组件
│   │   ├── components/     # 可复用组件
│   │   ├── views/          # 页面组件
│   │   ├── router/         # 路由配置
│   │   ├── store/          # 状态管理
│   │   ├── utils/          # 工具函数
│   │   └── assets/         # 静态资源
│   └── public/             # 公共资源
└── docs/                   # 文档
    └── API.md              # API文档
```

## 技术栈

### 后端
- Flask - Web框架
- Flask-SocketIO - WebSocket支持
- Celery - 异步任务队列
- Redis - 缓存和消息队列

### 前端
- Vue.js 3 - 前端框架
- Element Plus - UI组件库
- ECharts - 图表库
- Axios - HTTP客户端
- Socket.IO - WebSocket客户端

## 开发环境

### 后端启动
```bash
cd backend
pip install -r requirements.txt
python app.py
```

### 前端启动
```bash
cd frontend
npm install
npm run dev
```

## 功能模块

1. **可视化回测** - 交互式回测配置和结果展示
2. **实时交易** - 交易监控和手动操作
3. **系统监测** - 系统状态和市场数据监控
4. **数据管理** - 数据下载和质量管理
