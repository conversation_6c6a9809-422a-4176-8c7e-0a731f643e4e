---
type: "manual"
---

# Git 提交规范

为保证项目代码的可追溯性和自动化，所有 Git 提交都应遵循 Conventional Commits 规范，并结合中文开发环境的习惯。

## 核心理念

**类型和范围 (Type/Scope) 使用英文，主题和正文 (Subject/Body) 推荐使用中文，以便更清晰地描述意图。**

## 提交信息格式

```
<type>(<scope>): <subject>
<BLANK LINE>
<body>
<BLANK LINE>
<footer>
```

---

### 1. 页眉 (Header) - **必需**

页眉只有一行，包含**类型 (type)**、**范围 (scope)** 和 **主题 (subject)**。

#### **类型 (Type)** - 英文

必须是以下关键字之一，描述了本次提交的性质：

*   **feat**: 新功能 (feature)
*   **fix**: 修复 Bug
*   **docs**: 仅仅修改了文档 (documentation)
*   **style**: 不影响代码含义的修改 (空格、格式化、缺少分号等)
*   **refactor**: 代码重构，既不是修复 Bug 也不是添加新功能
*   **perf**: 提升性能的代码修改
*   **test**: 增加或修改测试
*   **build**: 修改项目构建系统或外部依赖 (例如: `pyproject.toml`, `npm`)
*   **ci**: 修改 CI 配置文件和脚本 (例如: GitHub Actions)
*   **chore**: 其他不修改 `src` 或 `tests` 目录的提交 (例如: 更新 `.gitignore`)
*   **revert**: 撤销一个先前的提交

#### **范围 (Scope)** - 英文，可选

用于说明本次提交影响的范围，通常是**模块名或功能名**。

*   **示例**: `feat(api)`, `fix(strategy)`, `docs(readme)`

#### **主题 (Subject)** - 中文

对本次修改的简洁描述，**强烈推荐使用中文**。

*   **规则**:
    *   清晰地描述做了什么，避免模糊不清。
    *   结尾不加句号 (`.`)。

*   **中英混搭示例**:
    *   `feat(auth): 实现 JWT 用户身份验证`
    *   `fix(parser): 正确处理空的 input 输入`
    *   `refactor(utils): 重构 `output_formatter` 以支持多种格式`

---

### 2. 正文 (Body) - 中文，可选

*   对本次修改的详细描述，解释修改的**原因**和**背景**。
*   应解释**为什么**做这次修改，而不仅仅是**做了什么**。
*   每行不超过 72 个字符。

---

### 3. 页脚 (Footer) - 可选

#### **重大更改 (Breaking Changes)**

如果当前代码与上一个版本不兼容，页脚应以 `BREAKING CHANGE:` 开头，后面是对变动的描述、理由和迁移方法。

```
BREAKING CHANGE: `user` 表的 `id` 字段现在是 UUID 而不是自增整数。
所有需要根据 ID 查询用户的服务，都必须更新其查询逻辑以支持字符串类型的 ID。
```

#### **关联 Issue**

如果本次提交关闭了某个 Issue，可以在页脚注明。

*   **示例**: `Closes #123`, `Fixes #456`

---

### 完整示例

#### 示例 1: 修复 Bug
```
fix(state): 修复在 state manager 中的线程竞争问题

旧的 state manager 使用了非线程安全的 dict，在多线程并发访问时
有概率导致数据错乱。

本次提交将标准的 `dict` 替换为 `threading.local`，确保每个线程
都拥有独立的 state 字典，从而解决了这个问题。

Closes #78
```

#### 示例 2: 添加新功能并包含重大更改
```
feat(api): 增加删除用户信息的 endpoint

为管理员添加了一个新的 `DELETE /api/v1/users/{id}` endpoint，
允许其永久删除某个用户的数据。

BREAKING CHANGE: `GET /api/v1/users` 这个 endpoint 的返回结果中，
不再包含已停用的用户。如果需要获取这些用户，必须显式地加上
`include_deactivated=true` 这个查询参数。
```