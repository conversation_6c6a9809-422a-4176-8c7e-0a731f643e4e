# Git 提交与分支规范 (中文开发者版)

为保证项目代码的稳定性、可追溯性和自动化，所有 Git 操作都应遵循本规范。本规范包含三个核心部分：

1.  **提交信息格式 (Commit Message)**
2.  **提交内容组织 (Commit Content)**
3.  **分支管理策略 (Branching Strategy)**

---

## 第一部分：提交信息格式 (Conventional Commits)

核心理念：**类型和范围 (Type/Scope) 使用英文，主题和正文 (Subject/Body) 推荐使用中文，以便更清晰地描述意图。**

### 提交信息结构

```
<type>(<scope>): <subject>
<BLANK LINE>
<body>
<BLANK LINE>
<footer>
```

(此部分详细内容省略，与之前版本相同...)

#### **类型 (Type)** - 英文
*   **feat**: 新功能
*   **fix**: 修复 Bug
*   **docs**: 修改文档
*   **style**: 代码格式
*   **refactor**: 代码重构
*   **perf**: 性能优化
*   **test**: 修改测试
*   **build**: 修改构建系统
*   **ci**: 修改 CI 配置
*   **chore**: 其他杂务
*   **revert**: 撤销提交

---

## 第二部分：提交内容组织 (Commit Content)

### 1. 保持提交的原子性 (Atomic Commits)
**一个提交只做一件事**。避免将多个不相关的改动混在一个提交里。

### 2. 按逻辑顺序组织提交
当一个功能需要多次提交才能完成时，应确保提交的顺序符合逻辑。

### 3. 提交前进行代码自审
在执行 `git commit` 前，请先使用 `git diff --staged` 检查本次将要提交的内容。

### 4. 确保提交是完整的
一个提交应该是一个**完整的功能单元**，提交后项目应能正常编译和运行。

---

## 第三部分：分支管理策略 (Branching Strategy)

为了隔离开发、保证主干分支的稳定，所有开发工作都应在**功能分支 (Feature Branch)** 上进行。严禁直接向 `main` 或 `develop` 分支提交代码。

### 核心原则

> **任何开发任务，无论大小，都应该创建一个新的分支。**

一个分支对应一个功能、一个修复或一个独立的任务。这保证了所有改动都是通过 Pull Request (PR) / Merge Request (MR) 合并的，为 Code Review 提供了保障。

### 标准工作流程

1.  **创建分支 (Create a Branch)**
    *   首先，确保你的主分支 (`main` 或 `develop`) 是最新的：
        ```bash
        git checkout main
        git pull origin main
        ```
    *   然后，基于主分支创建一个新的功能分支。分支命名应清晰、有描述性。
        ```bash
        # 推荐的分支命名: <type>/<short-description>
        git checkout -b feat/add-user-authentication
        git checkout -b fix/login-button-bug
        ```

2.  **本地提交 (Commit Locally)**
    *   在新建的分支上进行开发，并创建**一个或多个**符合规范的原子性提交。
        ```bash
        git add .
        git commit -m "feat(auth): 实现用户注册逻辑"
        ```

3.  **推送分支 (Push Branch)**
    *   将你的功能分支推送到远程仓库。
        ```bash
        git push -u origin feat/add-user-authentication
        ```

4.  **创建合并请求 (Create a Pull Request)**
    *   在代码托管平台（如 GitHub, GitLab）上，针对你刚才推送的分支，创建一个 Pull Request (PR) / Merge Request (MR)，目标分支通常是 `main` 或 `develop`。
    *   在 PR 的描述中，清晰地说明这个分支做了什么、为什么这么做，以及如何测试。

5.  **代码审查与合并 (Review and Merge)**
    *   等待团队成员进行 Code Review。Review 通过并且所有自动化检查（CI）都成功后，由项目负责人将该 PR 合并到主分支。

6.  **删除分支 (Delete Branch)**
    *   合并后，为了保持仓库的整洁，应删除已经合并的功能分支（远程和本地）。
        ```bash
        # 删除远程分支
        git push origin --delete feat/add-user-authentication
        
        # 切回主分支并删除本地分支
        git checkout main
        git branch -d feat/add-user-authentication
        ```

这个工作流确保了 `main` 分支的每一个提交都是一个经过审查的、完整的功能或修复，极大地提高了项目的稳定性和可维护性。